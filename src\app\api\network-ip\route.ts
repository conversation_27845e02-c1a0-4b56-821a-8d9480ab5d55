import { NextRequest, NextResponse } from 'next/server';
import { getBaseURL } from '@/lib/network-utils';

export async function GET(request: NextRequest) {
  try {
    // Get the network IP from the request
    const baseURL = getBaseURL(request);
    
    // Return the network IP as JSON
    return NextResponse.json({ 
      baseURL,
      networkIP: baseURL.replace('http://', '').replace('https://', ''),
      success: true 
    });
  } catch (error) {
    console.error('Error getting network IP:', error);
    
    // Fallback to localhost
    return NextResponse.json({ 
      baseURL: 'http://localhost:3000',
      networkIP: 'localhost:3000',
      success: false,
      error: 'Could not detect network IP'
    });
  }
}
