"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";

import { AlertCircle, ArrowLeft, Bell, Shield } from "lucide-react";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";

export default function NotificationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { notifications, markAsRead, isAdminMode } = useNotifications();
  const [notification, setNotification] = useState<Notification | null>(null);
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isHydrated, setIsHydrated] = useState(false);

  // Function to validate PDF URL
  const validatePdfUrl = async (pdfUrl: string) => {
    try {
      const response = await fetch(pdfUrl, { method: "HEAD" });
      if (!response.ok) {
        setPdfError("PDF file not found");
        return false;
      }
      setPdfError(null);
      return true;
    } catch (error) {
      console.error("Error validating PDF:", error);
      setPdfError("Failed to load PDF");
      return false;
    }
  };

  useEffect(() => {
    const checkAdminAndLoadNotification = async () => {
      // Set hydrated to true on client side
      setIsHydrated(true);
      setIsLoading(false);

      // Check if user has admin access
      if (!isAdminMode) {
        return;
      }

      const notificationId = params.id as string;

      // Try to find in local state first
      let foundNotification = notifications.find(
        (n) => n.id === notificationId
      );

      // If not found locally, fetch from database
      if (!foundNotification) {
        try {
          const response = await fetch(`/api/notifications/${notificationId}`);
          const data = await response.json();
          if (data.success) {
            foundNotification = {
              ...data.notification,
              createdAt: new Date(data.notification.createdAt),
            };
          }
        } catch (error) {
          console.error("Error fetching notification:", error);
        }
      }

      if (foundNotification) {
        console.log("Found notification:", foundNotification);
        console.log("PDF URL:", foundNotification.pdfUrl);
        console.log("PDF File Path:", foundNotification.pdfFilePath);
        setNotification(foundNotification);
        // Mark as read when viewing details
        if (!foundNotification.isRead) {
          markAsRead(foundNotification.id);
        }

        // Validate PDF URL if it exists
        if (foundNotification.pdfUrl) {
          console.log("Validating PDF URL:", foundNotification.pdfUrl);
          validatePdfUrl(foundNotification.pdfUrl);
        } else {
          console.log("No PDF URL found in notification");
        }
      }
    };

    checkAdminAndLoadNotification();
  }, [params.id, notifications, markAsRead, isAdminMode]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  // Show loading state while checking admin mode or during SSR
  if (isLoading || !isHydrated) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show access denied if not in admin mode
  if (!isAdminMode) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        <div className="text-center py-12">
          <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Admin Access Required</h3>
          <p className="text-muted-foreground mb-6">
            You need to enable admin mode to view notifications.
          </p>
          <Button onClick={() => router.push("/settings")}>
            <Shield className="h-4 w-4 mr-2" />
            Go to Settings
          </Button>
        </div>
      </div>
    );
  }

  if (!notification) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Notification Not Found
            </h3>
            <p className="text-muted-foreground mb-6">
              The notification you&apos;re looking for doesn&apos;t exist or has
              been removed.
            </p>
            <Button onClick={() => router.push("/")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {/* Fixed Sidebar - blank */}
      <div className="fixed left-0 top-0 w-64 h-screen bg-muted/30 border-r z-40">
        <div className="p-6">
          <div className="flex items-center gap-3 mb-6">
            <Bell className="h-6 w-6 text-primary" />
            <h2 className="text-lg font-semibold">Notification Details</h2>
          </div>

          {/* Empty sidebar content */}
        </div>
      </div>

      {/* Main Content */}
      <div className="ml-64 flex h-[calc(100vh-120px)]">
        {/* PDF Preview Area */}
        <div className="flex-1 flex flex-col">
          {notification.pdfUrl ? (
            <div className="h-full bg-background border-r">
              <div className="p-4 border-b">
                <h2 className="text-lg font-semibold">PDF Preview</h2>
                <p className="text-sm text-muted-foreground">
                  Preview of the uploaded PDF file
                </p>
              </div>
              <div className="flex-1 overflow-hidden p-4">
                <div className="w-full h-full bg-muted/20 rounded-lg overflow-hidden">
                  {pdfError ? (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground text-sm">
                          {pdfError}
                        </p>
                      </div>
                    </div>
                  ) : (
                    <iframe
                      src={notification.pdfUrl}
                      className="w-full h-full border-0 rounded-lg"
                      title="PDF Preview"
                    />
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-background">
              <div className="text-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No PDF Available</h3>
                <p className="text-muted-foreground">
                  This notification doesn't have an associated PDF file.
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Template Details */}
        <div className="w-80 bg-background flex flex-col overflow-hidden border-l">
          <div className="p-4 border-b">
            <h2 className="text-lg font-semibold">Template Details</h2>
          </div>
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Notification Information */}
              <div>
                <h3 className="text-sm font-semibold mb-3">
                  Notification Information
                </h3>
                <div className="space-y-3">
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">
                      Title
                    </Label>
                    <p className="text-xs bg-muted/50 p-2 rounded">
                      {notification.title}
                    </p>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">
                      Message
                    </Label>
                    <p className="text-xs bg-muted/50 p-2 rounded">
                      {notification.message}
                    </p>
                  </div>
                  {notification.pdfFileName && (
                    <div>
                      <Label className="text-xs font-medium text-muted-foreground">
                        File Name
                      </Label>
                      <p className="text-xs bg-muted/50 p-2 rounded">
                        {notification.pdfFileName}
                      </p>
                    </div>
                  )}
                  <div>
                    <Label className="text-xs font-medium text-muted-foreground">
                      Uploaded At
                    </Label>
                    <p className="text-xs bg-muted/50 p-2 rounded">
                      {formatDate(notification.createdAt.toString())}
                    </p>
                  </div>
                </div>
              </div>

              {/* Template Information */}
              {notification.pdfData && (
                <div className="space-y-6">
                  {/* Template Information */}
                  <div>
                    <h3 className="text-sm font-semibold mb-3">
                      Template Information
                    </h3>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Template ID
                        </Label>
                        <p className="text-xs font-mono bg-muted/50 p-2 rounded">
                          {notification.pdfData.templateId}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Template Name
                        </Label>
                        <p className="text-xs bg-muted/50 p-2 rounded">
                          {notification.pdfData.templateName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Layout Size
                        </Label>
                        <p className="text-xs bg-muted/50 p-2 rounded">
                          {notification.pdfData.layoutSize}
                        </p>
                      </div>
                      <div>
                        <Label className="text-xs font-medium text-muted-foreground">
                          Generated At
                        </Label>
                        <p className="text-xs bg-muted/50 p-2 rounded">
                          {formatDate(notification.pdfData.generatedAt)}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* User Data */}
                  <div>
                    <h3 className="text-sm font-semibold mb-3">User Data</h3>
                    <div className="space-y-2">
                      {Object.entries(notification.pdfData.userData).map(
                        ([key, value]) => (
                          <div key={key}>
                            <Label className="text-xs font-medium text-muted-foreground">
                              {key}
                            </Label>
                            <p className="text-xs bg-muted/50 p-2 rounded">
                              {value || (
                                <span className="italic text-muted-foreground">
                                  Empty
                                </span>
                              )}
                            </p>
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Embedded Photo */}
                  {notification.pdfData.photoBase64 && (
                    <div>
                      <h3 className="text-sm font-semibold mb-3">
                        Embedded Photo
                      </h3>
                      <div className="w-full">
                        <img
                          src={notification.pdfData.photoBase64}
                          alt="Embedded photo"
                          className="w-full h-auto border rounded-lg"
                        />
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
