import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';

// PATCH /api/notifications/bulk - Bulk operations (mark all as read)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, userId } = body;
    
    if (action === 'markAllAsRead') {
      const updatedCount = await NotificationModel.markAllAsRead(
        userId ? parseInt(userId) : undefined
      );
      
      return NextResponse.json({
        success: true,
        updatedCount,
        message: `${updatedCount} notifications marked as read`
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Invalid bulk action' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error performing bulk operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}
