import { getDatabase } from '../database';

// Generate UUID function for server-side use
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

export interface PDFData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: "A4" | "Letter";
}

export interface DatabaseNotification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  isRead: boolean;
  createdAt: string;
  updatedAt: string;
  userId?: number;
  pdfFileName?: string;
  pdfFilePath?: string;
  pdfData?: PDFData;
}

export interface CreateNotificationData {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  userId?: number;
  pdfFileName?: string;
  pdfFilePath?: string;
  pdfData?: PDFData;
}

// Notification database operations
export class NotificationModel {
  static async create(notificationData: CreateNotificationData): Promise<DatabaseNotification> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const id = generateUUID();
    
    const now = new Date().toISOString();

    const stmt = db.prepare(`
      INSERT INTO notifications (
        id, title, message, type, user_id, pdf_filename, pdf_file_path, pdf_data, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      notificationData.title,
      notificationData.message,
      notificationData.type,
      notificationData.userId || null,
      notificationData.pdfFileName || null,
      notificationData.pdfFilePath || null,
      notificationData.pdfData ? JSON.stringify(notificationData.pdfData) : null,
      now,
      now
    );
    
    return await this.findById(id) as DatabaseNotification;
  }

  static async findById(id: string): Promise<DatabaseNotification | null> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('SELECT * FROM notifications WHERE id = ?');
    const row = stmt.get(id) as any;
    
    if (!row) return null;
    
    return this.mapRowToNotification(row);
  }

  static async findAll(limit?: number): Promise<DatabaseNotification[]> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const query = limit 
      ? 'SELECT * FROM notifications ORDER BY created_at DESC LIMIT ?'
      : 'SELECT * FROM notifications ORDER BY created_at DESC';
    
    const stmt = db.prepare(query);
    const rows = limit ? stmt.all(limit) : stmt.all();
    
    return (rows as any[]).map(row => this.mapRowToNotification(row));
  }

  static async findByUserId(userId: number, limit?: number): Promise<DatabaseNotification[]> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const query = limit 
      ? 'SELECT * FROM notifications WHERE user_id = ? OR user_id IS NULL ORDER BY created_at DESC LIMIT ?'
      : 'SELECT * FROM notifications WHERE user_id = ? OR user_id IS NULL ORDER BY created_at DESC';
    
    const stmt = db.prepare(query);
    const rows = limit ? stmt.all(userId, limit) : stmt.all(userId);
    
    return (rows as any[]).map(row => this.mapRowToNotification(row));
  }

  static async markAsRead(id: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare(`
      UPDATE notifications 
      SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `);
    
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static async markAllAsRead(userId?: number): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let result;
    
    if (userId) {
      stmt = db.prepare(`
        UPDATE notifications 
        SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP 
        WHERE (user_id = ? OR user_id IS NULL) AND is_read = FALSE
      `);
      result = stmt.run(userId);
    } else {
      stmt = db.prepare(`
        UPDATE notifications 
        SET is_read = TRUE, updated_at = CURRENT_TIMESTAMP 
        WHERE is_read = FALSE
      `);
      result = stmt.run();
    }
    
    return result.changes;
  }

  static async delete(id: string): Promise<boolean> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    const stmt = db.prepare('DELETE FROM notifications WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0;
  }

  static async deleteAll(userId?: number): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let result;
    
    if (userId) {
      stmt = db.prepare('DELETE FROM notifications WHERE user_id = ? OR user_id IS NULL');
      result = stmt.run(userId);
    } else {
      stmt = db.prepare('DELETE FROM notifications');
      result = stmt.run();
    }
    
    return result.changes;
  }

  static async getUnreadCount(userId?: number): Promise<number> {
    if (typeof window !== 'undefined') {
      throw new Error('Database operations can only be performed on the server side');
    }

    const db = await getDatabase();
    let stmt;
    let result;
    
    if (userId) {
      stmt = db.prepare(`
        SELECT COUNT(*) as count 
        FROM notifications 
        WHERE (user_id = ? OR user_id IS NULL) AND is_read = FALSE
      `);
      result = stmt.get(userId) as any;
    } else {
      stmt = db.prepare('SELECT COUNT(*) as count FROM notifications WHERE is_read = FALSE');
      result = stmt.get() as any;
    }
    
    return result.count;
  }

  private static mapRowToNotification(row: any): DatabaseNotification {
    return {
      id: row.id,
      title: row.title,
      message: row.message,
      type: row.type,
      isRead: Boolean(row.is_read),
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      userId: row.user_id,
      pdfFileName: row.pdf_filename,
      pdfFilePath: row.pdf_file_path,
      pdfData: row.pdf_data ? JSON.parse(row.pdf_data) : undefined
    };
  }
}
