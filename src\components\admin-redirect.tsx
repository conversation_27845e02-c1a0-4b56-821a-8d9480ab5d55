"use client";

import { useEffect, useState } from "react";
import { useRouter, usePathname } from "next/navigation";

export function AdminRedirect() {
  const router = useRouter();
  const pathname = usePathname();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);

    const checkAdminModeAndRedirect = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";

        if (savedAdminMode) {
          // If admin mode is enabled and user is not on admin page or admin-related pages
          if (!isAdminRelatedPage(pathname)) {
            router.push("/admin");
          }
        } else {
          // If admin mode is disabled and user is on admin-related pages (except settings)
          if (isAdminRelatedPage(pathname) && pathname !== "/settings") {
            router.push("/");
          }
        }
      }
    };

    // Check immediately after hydration
    if (isHydrated) {
      checkAdminModeAndRedirect();
    }

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminModeAndRedirect();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("adminModeChanged", handleAdminModeChange);
      return () => {
        window.removeEventListener("adminModeChanged", handleAdminModeChange);
      };
    }
  }, [router, pathname, isHydrated]);

  // Don't render anything during SSR
  if (!isHydrated) {
    return null;
  }

  return null;
}

// Helper function to determine if current page is admin-related
function isAdminRelatedPage(pathname: string): boolean {
  const adminRelatedPaths = [
    "/admin",
    "/settings",
    "/templates/add",
    "/templates/manage",
    "/notifications/", // This covers notification detail pages like /notifications/[id] but NOT /notifications/upload
  ];

  // Check if current path starts with any admin-related path
  // But exclude /notifications/upload specifically
  if (pathname === "/notifications/upload") {
    return false;
  }

  return adminRelatedPaths.some((path) => pathname.startsWith(path));
}
