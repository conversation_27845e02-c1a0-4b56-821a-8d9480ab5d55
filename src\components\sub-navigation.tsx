"use client";

import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { BackButton } from "@/components/back-button";
import { BreadcrumbNav } from "@/components/breadcrumb-nav";

export function SubNavigation() {
  const pathname = usePathname();
  const [isMounted, setIsMounted] = useState(false);
  const [adminMode, setAdminMode] = useState(false);

  useEffect(() => {
    setIsMounted(true);

    const checkAdminMode = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";
        setAdminMode(savedAdminMode);
      }
    };

    checkAdminMode();

    window.addEventListener("adminModeChanged", checkAdminMode);
    return () => {
      window.removeEventListener("adminModeChanged", checkAdminMode);
    };
  }, []);

  // Don't show navigation on home page
  if (pathname === "/") {
    return null;
  }

  // Check if we're on admin page or notification detail page (only after hydration)
  const isAdminPage =
    isMounted &&
    adminMode &&
    (pathname === "/admin" ||
      pathname.startsWith("/notifications/") ||
      pathname === "/settings");

  return (
    <div className={isAdminPage ? "ml-64" : ""}>
      <div className="px-4 py-3">
        <div className="flex items-center gap-4">
          <BackButton />
          <BreadcrumbNav />
        </div>
      </div>
    </div>
  );
}
