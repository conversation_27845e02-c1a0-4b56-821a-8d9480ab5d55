import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel, CreateNotificationData } from '@/lib/models/notification';

// GET /api/notifications - Get all notifications or notifications for a specific user
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    const limit = searchParams.get('limit');
    
    let notifications;
    
    if (userId) {
      notifications = await NotificationModel.findByUserId(
        parseInt(userId), 
        limit ? parseInt(limit) : undefined
      );
    } else {
      notifications = await NotificationModel.findAll(
        limit ? parseInt(limit) : undefined
      );
    }
    
    // Transform notifications to include pdfUrl if pdfFilePath exists
    const transformedNotifications = notifications.map(notification => ({
      ...notification,
      pdfUrl: notification.pdfFilePath ? `/api/notifications/${notification.id}/pdf` : undefined
    }));

    return NextResponse.json({
      success: true,
      notifications: transformedNotifications
    });
  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notifications' },
      { status: 500 }
    );
  }
}

// POST /api/notifications - Create a new notification
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, message, type, userId, pdfFileName, pdfFilePath, pdfData } = body;
    
    if (!title || !message || !type) {
      return NextResponse.json(
        { success: false, error: 'Title, message, and type are required' },
        { status: 400 }
      );
    }
    
    const notificationData: CreateNotificationData = {
      title,
      message,
      type,
      userId: userId ? parseInt(userId) : undefined,
      pdfFileName,
      pdfFilePath,
      pdfData
    };
    
    const notification = await NotificationModel.create(notificationData);
    
    return NextResponse.json({
      success: true,
      notification
    });
  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create notification' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications - Delete all notifications (optionally for a specific user)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');
    
    const deletedCount = await NotificationModel.deleteAll(
      userId ? parseInt(userId) : undefined
    );
    
    return NextResponse.json({
      success: true,
      deletedCount
    });
  } catch (error) {
    console.error('Error deleting notifications:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete notifications' },
      { status: 500 }
    );
  }
}
