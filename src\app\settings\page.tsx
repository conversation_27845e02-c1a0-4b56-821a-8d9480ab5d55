"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Shield, Plus, Settings, User, QrCode } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";
import { AdminAuthDialog } from "@/components/admin-auth-dialog";
import { toast } from "sonner";
import { QRCodeDialog } from "@/components/qr-code-dialog";

export default function SettingsPage() {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [adminMode, setAdminMode] = useState(false);
  const [showAuthDialog, setShowAuthDialog] = useState(false);
  const [showQRDialog, setShowQRDialog] = useState(false);

  // Load admin mode from localStorage on component mount
  useEffect(() => {
    const savedAdminMode = localStorage.getItem("adminMode") === "true";
    setAdminMode(savedAdminMode);
  }, []);

  // Listen for admin mode changes
  useEffect(() => {
    const handleAdminModeChange = () => {
      const savedAdminMode = localStorage.getItem("adminMode") === "true";
      setAdminMode(savedAdminMode);
    };

    window.addEventListener("adminModeChanged", handleAdminModeChange);
    return () => {
      window.removeEventListener("adminModeChanged", handleAdminModeChange);
    };
  }, []);

  const handleAdminModeToggle = (checked: boolean) => {
    if (checked && !isAuthenticated) {
      // Show authentication dialog
      setShowAuthDialog(true);
      return;
    }

    setAdminMode(checked);
    localStorage.setItem("adminMode", checked.toString());
    // Dispatch custom event to notify other components
    window.dispatchEvent(new Event("adminModeChanged"));
  };

  const handleAuthSuccess = () => {
    setAdminMode(true);
    localStorage.setItem("adminMode", "true");
    window.dispatchEvent(new Event("adminModeChanged"));
    toast.success("Admin mode enabled! Redirecting to admin dashboard...");

    // Redirect to admin home page after a short delay
    setTimeout(() => {
      router.push("/admin");
    }, 1500);
  };

  const handleAddTemplate = () => {
    if (adminMode) {
      router.push("/templates/add");
    }
  };

  const handleManageTemplates = () => {
    if (adminMode) {
      router.push("/templates/manage");
    }
  };

  return (
    <>
      {adminMode && (
        <div className="fixed left-0 top-0 w-64 h-screen bg-muted/30 border-r z-40">
          <div className="p-6">
            <div className="flex items-center gap-3 mb-6">
              <Settings className="h-6 w-6 text-primary" />
              <h2 className="text-lg font-semibold">Settings</h2>
            </div>

            {/* Empty sidebar */}
          </div>
        </div>
      )}

      <div className={adminMode ? "ml-64 container mx-auto px-4 py-8 max-w-2xl" : "container mx-auto px-4 py-8 max-w-2xl"}>
      <div className="flex items-center gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-semibold text-foreground">Settings</h1>
          <p className="text-muted-foreground">
            Manage your application preferences and template settings.
          </p>
        </div>
      </div>

      <div className="space-y-6">
        {/* Admin Mode Control */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Admin Mode
            </CardTitle>
            <CardDescription>
              Enable admin mode to access template management and other
              administrative features. Authentication is required.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Label htmlFor="admin-toggle" className="text-sm font-medium">
                    Admin Mode
                  </Label>
                  {isAuthenticated && (
                    <User className="h-3 w-3 text-green-500" />
                  )}
                </div>
                <Switch
                  id="admin-toggle"
                  checked={adminMode}
                  onCheckedChange={handleAdminModeToggle}
                />
              </div>

              {isAuthenticated && (
                <div className="text-xs text-muted-foreground">
                  Logged in as: {user?.username}
                </div>
              )}

              {adminMode && (
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-green-500" />
                  <span className="text-sm text-green-600 dark:text-green-400">
                    Admin mode is enabled - you have access to all
                    administrative features
                  </span>
                </div>
              )}

              {!adminMode && (
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 rounded-full bg-gray-400" />
                  <span className="text-sm text-muted-foreground">
                    Admin mode is disabled - enable to access administrative
                    features
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Template Management */}
        <Card>
          <CardHeader>
            <CardTitle>Template Management</CardTitle>
            <CardDescription>
              Add new templates or manage existing ones.{" "}
              {!adminMode && "Admin mode required."}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={handleAddTemplate}
              className="w-full justify-start"
              variant="outline"
              disabled={!adminMode}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Template
            </Button>

            <Button
              onClick={handleManageTemplates}
              className="w-full justify-start"
              variant="outline"
              disabled={!adminMode}
            >
              <Settings className="h-4 w-4 mr-2" />
              Manage Templates
            </Button>
          </CardContent>
        </Card>

        {/* QR Code Generation - Admin Only */}
        {adminMode && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                QR Code for Mobile Access
              </CardTitle>
              <CardDescription>
                Generate a QR code that allows mobile devices to automatically
                connect to your hotspot and access the upload page.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => setShowQRDialog(true)}
                className="w-full justify-start"
                variant="outline"
              >
                <QrCode className="h-4 w-4 mr-2" />
                Generate QR Code
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Admin Authentication Dialog */}
      <AdminAuthDialog
        open={showAuthDialog}
        onOpenChange={setShowAuthDialog}
        onAuthSuccess={handleAuthSuccess}
      />

      {/* QR Code Dialog */}
      <QRCodeDialog open={showQRDialog} onOpenChange={setShowQRDialog} />
    </div>
    </>
  );
}
