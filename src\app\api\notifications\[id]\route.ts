import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';

// GET /api/notifications/[id] - Get a specific notification
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notification = await NotificationModel.findById(params.id);
    
    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    // Transform the notification to include pdfUrl if pdfFilePath exists
    const transformedNotification = {
      ...notification,
      pdfUrl: notification.pdfFilePath ? `/api/notifications/${params.id}/pdf` : undefined
    };

    return NextResponse.json({
      success: true,
      notification: transformedNotification
    });
  } catch (error) {
    console.error('Error fetching notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch notification' },
      { status: 500 }
    );
  }
}

// PATCH /api/notifications/[id] - Update a notification (e.g., mark as read)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { isRead } = body;
    
    if (isRead === true) {
      const success = await NotificationModel.markAsRead(params.id);
      
      if (!success) {
        return NextResponse.json(
          { success: false, error: 'Notification not found' },
          { status: 404 }
        );
      }
      
      const updatedNotification = await NotificationModel.findById(params.id);
      
      return NextResponse.json({
        success: true,
        notification: updatedNotification
      });
    }
    
    return NextResponse.json(
      { success: false, error: 'Invalid update operation' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update notification' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/[id] - Delete a specific notification
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const success = await NotificationModel.delete(params.id);
    
    if (!success) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      message: 'Notification deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete notification' },
      { status: 500 }
    );
  }
}
