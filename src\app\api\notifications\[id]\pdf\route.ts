import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';
import fs from 'fs';
import path from 'path';

// GET /api/notifications/[id]/pdf - Serve the PDF file for a notification
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const notification = await NotificationModel.findById(params.id);
    
    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }
    
    if (!notification.pdfFilePath) {
      return NextResponse.json(
        { success: false, error: 'No PDF file associated with this notification' },
        { status: 404 }
      );
    }
    
    // Construct the full path to the PDF file
    const fullPath = path.join(process.cwd(), 'public', notification.pdfFilePath);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return NextResponse.json(
        { success: false, error: 'PDF file not found on disk' },
        { status: 404 }
      );
    }
    
    // Read the file
    const fileBuffer = fs.readFileSync(fullPath);
    
    // Return the PDF file with proper headers
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `inline; filename="${notification.pdfFileName || 'document.pdf'}"`,
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error serving PDF file:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to serve PDF file' },
      { status: 500 }
    );
  }
}
