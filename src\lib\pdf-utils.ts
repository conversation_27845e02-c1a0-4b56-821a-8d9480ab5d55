// PDF utility functions for client-side PDF processing

// Dynamic import function to load PDF.js only on client side
async function loadPdfJs() {
  if (typeof window === 'undefined') {
    throw new Error('PDF.js can only be used on the client side');
  }

  const pdfjsLib = await import('pdfjs-dist');

  // Set up the worker for Next.js environment - use local worker file
  pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.mjs';

  return pdfjsLib;
}

/**
 * Convert a PDF file to an image (first page)
 * @param file - The PDF file to convert
 * @param scale - Scale factor for rendering (default: 2.0, optimized for mobile)
 * @returns Promise that resolves to a data URL of the rendered image
 */
export async function pdfToImage(file: File, scale: number = 2.0): Promise<string> {
  try {
    // Load PDF.js dynamically (client-side only)
    const pdfjsLib = await loadPdfJs();

    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    // Get the first page
    const page = await pdf.getPage(1);
    
    // Create a canvas element
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    if (!context) {
      throw new Error('Could not get canvas context');
    }
    
    // Calculate the viewport
    const viewport = page.getViewport({ scale });
    
    // Set canvas dimensions
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    
    // Render the page
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };
    
    await page.render(renderContext).promise;
    
    // Convert canvas to data URL with optimized quality for mobile
    return canvas.toDataURL('image/jpeg', 0.85); // Reduced from 0.95 to 0.85 for better performance
    
  } catch (error) {
    console.error('Error converting PDF to image:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('DOMMatrix')) {
        throw new Error('PDF.js requires a browser environment with DOM support');
      } else if (error.message.includes('worker')) {
        throw new Error('PDF worker failed to load. Please check your internet connection.');
      }
    }

    throw new Error(`Failed to convert PDF to image: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Convert multiple PDF pages to images
 * @param file - The PDF file to convert
 * @param maxPages - Maximum number of pages to convert (default: 1)
 * @param scale - Scale factor for rendering (default: 2.0, optimized for mobile)
 * @returns Promise that resolves to an array of data URLs
 */
export async function pdfToImages(file: File, maxPages: number = 1, scale: number = 2.0): Promise<string[]> {
  try {
    // Load PDF.js dynamically (client-side only)
    const pdfjsLib = await loadPdfJs();

    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();

    // Load the PDF document
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    const numPages = Math.min(pdf.numPages, maxPages);
    const images: string[] = [];
    
    // Convert each page to image
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      
      // Create a canvas element
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      
      if (!context) {
        throw new Error('Could not get canvas context');
      }
      
      // Calculate the viewport
      const viewport = page.getViewport({ scale });
      
      // Set canvas dimensions
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      
      // Render the page
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      
      await page.render(renderContext).promise;
      
      // Convert canvas to data URL with optimized quality and add to array
      images.push(canvas.toDataURL('image/jpeg', 0.85)); // Optimized for mobile performance
    }
    
    return images;
    
  } catch (error) {
    console.error('Error converting PDF pages to images:', error);

    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('DOMMatrix')) {
        throw new Error('PDF.js requires a browser environment with DOM support');
      } else if (error.message.includes('worker')) {
        throw new Error('PDF worker failed to load. Please check your internet connection.');
      }
    }

    throw new Error(`Failed to convert PDF pages to images: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
