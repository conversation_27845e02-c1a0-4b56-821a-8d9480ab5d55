"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  FileText,
  Upload,
  Eye,
  Download,
  ArrowLeft,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import { replacePlaceholders } from "@/lib/templates";

interface Template {
  id: string;
  name: string;
  description: string;
  filename: string;
  placeholders: string[];
  layoutSize: "A4" | "Letter";
  uploadedAt: string;
  hasApplicantPhoto?: boolean;
  content?: string;
}

interface EmbeddedData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: "A4" | "Letter";
}

export default function TemplateDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [template, setTemplate] = useState<Template | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [uploadedPdf, setUploadedPdf] = useState<File | null>(null);
  const [embeddedData, setEmbeddedData] = useState<EmbeddedData | null>(null);
  const [isProcessingPdf, setIsProcessingPdf] = useState(false);
  const [pdfUrl, setPdfUrl] = useState<string | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState<string>("");

  const loadTemplate = useCallback(
    async (id: string) => {
      try {
        const response = await fetch(`/api/templates/${id}`);
        if (!response.ok) {
          if (response.status === 404) {
            toast.error("Template not found");
            router.push("/");
            return;
          }
          throw new Error("Failed to load template");
        }
        const templateData = await response.json();
        setTemplate(templateData);
      } catch (error) {
        console.error("Error loading template:", error);
        toast.error("Failed to load template");
        router.push("/");
      } finally {
        setIsLoading(false);
      }
    },
    [router]
  );

  useEffect(() => {
    if (params.id) {
      loadTemplate(params.id as string);
    }
  }, [params.id, loadTemplate]);

  const handlePdfUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== "application/pdf") {
      toast.error("Please upload a PDF file");
      return;
    }

    setIsProcessingPdf(true);
    setUploadedPdf(file);

    try {
      // Parse the PDF to extract embedded data
      const formData = new FormData();
      formData.append("file", file);

      const parseResponse = await fetch("/api/pdf/parse", {
        method: "POST",
        body: formData,
      });

      if (!parseResponse.ok) {
        throw new Error("Failed to parse PDF");
      }

      const parseResult = await parseResponse.json();

      if (parseResult.hasEmbeddedData) {
        setEmbeddedData(parseResult.data);

        // Create object URL for PDF display
        const url = URL.createObjectURL(file);
        setPdfUrl(url);

        toast.success("PDF uploaded and embedded data extracted successfully!");
      } else {
        toast.error("No embedded data found in this PDF");
        setUploadedPdf(null);
      }
    } catch (error) {
      console.error("Error processing PDF:", error);
      toast.error("Failed to process PDF");
      setUploadedPdf(null);
    } finally {
      setIsProcessingPdf(false);
    }
  };

  const generatePreview = async () => {
    if (!template || !embeddedData) return;

    try {
      // Generate the document using the embedded data
      const response = await fetch("/api/templates/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          templateId: template.id,
          data: embeddedData.userData,
          photoPath: embeddedData.photoBase64
            ? `data:image/jpeg;base64,${embeddedData.photoBase64}`
            : null,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to generate preview");
      }

      const result = await response.json();
      setPreviewHtml(result.htmlContent);
      setShowPreview(true);
    } catch (error) {
      console.error("Error generating preview:", error);
      toast.error("Failed to generate preview");
    }
  };

  const clearUploadedPdf = () => {
    setUploadedPdf(null);
    setEmbeddedData(null);
    if (pdfUrl) {
      URL.revokeObjectURL(pdfUrl);
      setPdfUrl(null);
    }
    setShowPreview(false);
    setPreviewHtml("");
  };

  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading template...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!template) {
    return null;
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-semibold text-foreground">
            {template.name}
          </h1>
          <p className="text-muted-foreground">{template.description}</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Column - Template Info & PDF Upload */}
        <div className="space-y-6">
          {/* Template Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Template Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Layout Size</Label>
                <p className="text-sm text-muted-foreground">
                  {template.layoutSize}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">
                  Placeholders ({template.placeholders.length})
                </Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {template.placeholders.map((placeholder, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md font-mono"
                    >
                      {placeholder}
                    </span>
                  ))}
                </div>
              </div>
              <div>
                <Label className="text-sm font-medium">
                  Has Applicant Photo
                </Label>
                <p className="text-sm text-muted-foreground">
                  {template.hasApplicantPhoto ? "Yes" : "No"}
                </p>
              </div>
            </CardContent>
          </Card>

          {/* PDF Upload */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Generated PDF
              </CardTitle>
              <CardDescription>
                Upload a PDF generated from this template to extract and display
                the embedded data
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!uploadedPdf ? (
                <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                  <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Upload PDF File</p>
                    <p className="text-xs text-muted-foreground">
                      Select a PDF file generated from this template
                    </p>
                  </div>
                  <Input
                    type="file"
                    accept=".pdf"
                    onChange={handlePdfUpload}
                    disabled={isProcessingPdf}
                    className="mt-4"
                  />
                  {isProcessingPdf && (
                    <div className="mt-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                      <p className="text-xs text-muted-foreground mt-2">
                        Processing PDF...
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <FileText className="h-8 w-8 text-primary" />
                      <div>
                        <p className="font-medium text-sm">
                          {uploadedPdf.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {(uploadedPdf.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearUploadedPdf}
                    >
                      Remove
                    </Button>
                  </div>

                  {embeddedData && (
                    <div className="space-y-4">
                      <div className="flex items-center gap-2 text-green-600">
                        <AlertCircle className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          Embedded data found!
                        </span>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm font-medium">
                            Generated At
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {new Date(
                              embeddedData.generatedAt
                            ).toLocaleString()}
                          </p>
                        </div>

                        <div>
                          <Label className="text-sm font-medium">
                            Template Match
                          </Label>
                          <p className="text-sm text-muted-foreground">
                            {embeddedData.templateId === template.id ? (
                              <span className="text-green-600">
                                ✓ Matches current template
                              </span>
                            ) : (
                              <span className="text-orange-600">
                                ⚠ Different template (
                                {embeddedData.templateName})
                              </span>
                            )}
                          </p>
                        </div>

                        <div>
                          <Label className="text-sm font-medium">
                            Data Fields (
                            {Object.keys(embeddedData.userData).length})
                          </Label>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {Object.entries(embeddedData.userData)
                              .slice(0, 3)
                              .map(([key, value]) => (
                                <span
                                  key={key}
                                  className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md font-mono"
                                  title={`${key}: ${value}`}
                                >
                                  {key}
                                </span>
                              ))}
                            {Object.keys(embeddedData.userData).length > 3 && (
                              <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                                +{Object.keys(embeddedData.userData).length - 3}{" "}
                                more
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <Button
                          onClick={generatePreview}
                          size="sm"
                          className="flex-1"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          Preview Template
                        </Button>
                        {pdfUrl && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.open(pdfUrl, "_blank")}
                          >
                            <Download className="h-4 w-4 mr-2" />
                            View PDF
                          </Button>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Preview */}
        <div className="space-y-6">
          {showPreview && previewHtml && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Template Preview
                </CardTitle>
                <CardDescription>
                  Preview of the template filled with embedded data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg overflow-hidden">
                  <iframe
                    srcDoc={previewHtml}
                    className="w-full h-[600px] border-0"
                    title="Template Preview"
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {pdfUrl && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Original PDF
                </CardTitle>
                <CardDescription>
                  The uploaded PDF file with embedded data
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg overflow-hidden">
                  <iframe
                    src={pdfUrl}
                    className="w-full h-[600px] border-0"
                    title="Uploaded PDF"
                  />
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
