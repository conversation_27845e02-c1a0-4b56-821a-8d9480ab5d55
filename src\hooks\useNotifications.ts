"use client";

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

export interface PDFData {
  templateId: string;
  templateName: string;
  placeholders: string[];
  userData: Record<string, string>;
  photoBase64?: string;
  generatedAt: string;
  layoutSize: "A4" | "Letter";
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  isRead: boolean;
  createdAt: Date;
  pdfFileName?: string;
  pdfUrl?: string;
  pdfFilePath?: string;
  pdfData?: PDFData;
  userId?: number;
}

export interface CreateNotificationData {
  title: string;
  message: string;
  type: "info" | "success" | "warning" | "error";
  pdfFileName?: string;
  pdfUrl?: string;
  pdfFilePath?: string;
  pdfData?: PDFData;
}

// Query keys
const QUERY_KEYS = {
  notifications: ['notifications'] as const,
  notification: (id: string) => ['notifications', id] as const,
  unreadCount: ['notifications', 'unread-count'] as const,
};

// API functions
const fetchNotifications = async (): Promise<Notification[]> => {
  const response = await fetch('/api/notifications');
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to fetch notifications');
  }
  
  return data.notifications.map((n: any) => ({
    ...n,
    createdAt: new Date(n.createdAt),
  }));
};

const fetchNotification = async (id: string): Promise<Notification> => {
  const response = await fetch(`/api/notifications/${id}`);
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to fetch notification');
  }
  
  return {
    ...data.notification,
    createdAt: new Date(data.notification.createdAt),
  };
};

const createNotification = async (notificationData: CreateNotificationData): Promise<Notification> => {
  const response = await fetch('/api/notifications', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(notificationData),
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to create notification');
  }
  
  return {
    ...data.notification,
    createdAt: new Date(data.notification.createdAt),
  };
};

const markNotificationAsRead = async (id: string): Promise<Notification> => {
  const response = await fetch(`/api/notifications/${id}`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ isRead: true }),
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to mark notification as read');
  }
  
  return {
    ...data.notification,
    createdAt: new Date(data.notification.createdAt),
  };
};

const markAllNotificationsAsRead = async (): Promise<void> => {
  const response = await fetch('/api/notifications/bulk', {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ action: 'markAllAsRead' }),
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to mark all notifications as read');
  }
};

const deleteNotification = async (id: string): Promise<void> => {
  const response = await fetch(`/api/notifications/${id}`, {
    method: 'DELETE',
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to delete notification');
  }
};

const clearAllNotifications = async (): Promise<void> => {
  const response = await fetch('/api/notifications', {
    method: 'DELETE',
  });
  
  const data = await response.json();
  
  if (!data.success) {
    throw new Error(data.error || 'Failed to clear all notifications');
  }
};

// Custom hooks
export const useNotificationsQuery = (enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.notifications,
    queryFn: fetchNotifications,
    enabled,
    staleTime: 10 * 1000, // 10 seconds
    refetchInterval: enabled ? 30 * 1000 : false, // Refetch every 30 seconds when enabled
    refetchOnWindowFocus: true, // Refetch when window gains focus
    refetchOnMount: true, // Always refetch on mount
  });
};

export const useNotificationQuery = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: QUERY_KEYS.notification(id),
    queryFn: () => fetchNotification(id),
    enabled: enabled && !!id,
  });
};

export const useCreateNotificationMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createNotification,
    onMutate: async (newNotificationData) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.notifications });

      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData<Notification[]>(QUERY_KEYS.notifications);

      // Create optimistic notification with temporary ID
      const optimisticNotification: Notification = {
        id: `temp-${Date.now()}`,
        ...newNotificationData,
        isRead: false,
        createdAt: new Date(),
      };

      // Optimistically update to the new value - always update cache
      queryClient.setQueryData<Notification[]>(QUERY_KEYS.notifications, (old) => {
        return old ? [optimisticNotification, ...old] : [optimisticNotification];
      });

      return { previousNotifications, optimisticNotification };
    },
    onSuccess: (newNotification, variables, context) => {
      // Replace the optimistic notification with the real one from server
      queryClient.setQueryData<Notification[]>(QUERY_KEYS.notifications, (old) => {
        if (!old) return [newNotification];

        // Replace the optimistic notification with the real one
        return old.map(notification =>
          notification.id === context?.optimisticNotification.id
            ? newNotification
            : notification
        );
      });

      // Force invalidate and refetch to ensure fresh data
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.notifications });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.unreadCount });

      // Show success toast
      toast.success('Notification created successfully');
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(QUERY_KEYS.notifications, context.previousNotifications);
      }
      console.error('Error creating notification:', error);
      toast.error('Failed to create notification');
    },
  });
};

export const useMarkAsReadMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: markNotificationAsRead,
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.notifications });
      
      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData<Notification[]>(QUERY_KEYS.notifications);
      
      // Optimistically update to the new value
      queryClient.setQueryData<Notification[]>(QUERY_KEYS.notifications, (old) => {
        return old?.map((notification) =>
          notification.id === id
            ? { ...notification, isRead: true }
            : notification
        ) || [];
      });
      
      return { previousNotifications };
    },
    onError: (error, id, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(QUERY_KEYS.notifications, context.previousNotifications);
      }
      console.error('Error marking notification as read:', error);
      toast.error('Failed to mark notification as read');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.notifications });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.unreadCount });
    },
  });
};

export const useMarkAllAsReadMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: markAllNotificationsAsRead,
    onMutate: async () => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.notifications });
      
      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData<Notification[]>(QUERY_KEYS.notifications);
      
      // Optimistically update to the new value
      queryClient.setQueryData<Notification[]>(QUERY_KEYS.notifications, (old) => {
        return old?.map((notification) => ({ ...notification, isRead: true })) || [];
      });
      
      return { previousNotifications };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(QUERY_KEYS.notifications, context.previousNotifications);
      }
      console.error('Error marking all notifications as read:', error);
      toast.error('Failed to mark all notifications as read');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.notifications });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.unreadCount });
    },
  });
};

export const useDeleteNotificationMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: deleteNotification,
    onMutate: async (id) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.notifications });
      
      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData<Notification[]>(QUERY_KEYS.notifications);
      
      // Optimistically update to the new value
      queryClient.setQueryData<Notification[]>(QUERY_KEYS.notifications, (old) => {
        return old?.filter((notification) => notification.id !== id) || [];
      });
      
      return { previousNotifications };
    },
    onError: (error, id, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(QUERY_KEYS.notifications, context.previousNotifications);
      }
      console.error('Error deleting notification:', error);
      toast.error('Failed to delete notification');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.notifications });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.unreadCount });
    },
  });
};

export const useClearAllNotificationsMutation = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: clearAllNotifications,
    onMutate: async () => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: QUERY_KEYS.notifications });
      
      // Snapshot the previous value
      const previousNotifications = queryClient.getQueryData<Notification[]>(QUERY_KEYS.notifications);
      
      // Optimistically update to the new value
      queryClient.setQueryData<Notification[]>(QUERY_KEYS.notifications, []);
      
      return { previousNotifications };
    },
    onError: (error, variables, context) => {
      // Rollback on error
      if (context?.previousNotifications) {
        queryClient.setQueryData(QUERY_KEYS.notifications, context.previousNotifications);
      }
      console.error('Error clearing all notifications:', error);
      toast.error('Failed to clear all notifications');
    },
    onSettled: () => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.notifications });
      queryClient.invalidateQueries({ queryKey: QUERY_KEYS.unreadCount });
    },
  });
};
