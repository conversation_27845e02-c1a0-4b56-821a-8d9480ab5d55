"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON>, <PERSON>tings, Sun, Moon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useRouter, usePathname } from "next/navigation";
import { useTheme } from "@/components/theme-provider";
import { useAuth } from "@/contexts/auth-context";
import { NotificationDropdown } from "@/components/notification-dropdown";
import { toast } from "sonner";

export function Header() {
  const router = useRouter();
  const pathname = usePathname();
  const { theme, setTheme } = useTheme();
  const { isAuthenticated } = useAuth();
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [adminMode, setAdminMode] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // Check if we're on an admin-related page while admin mode is enabled
  const isAdminPage =
    isMounted &&
    adminMode &&
    (pathname === "/admin" ||
      pathname.startsWith("/notifications/") ||
      pathname === "/settings");

  // Update isDarkMode when theme changes
  useEffect(() => {
    setIsDarkMode(theme === "dark");
  }, [theme]);

  // Check admin mode on component mount and listen for changes
  useEffect(() => {
    setIsHydrated(true);
    setIsMounted(true);

    const checkAdminMode = () => {
      if (typeof window !== "undefined") {
        const savedAdminMode = localStorage.getItem("adminMode") === "true";
        setAdminMode(savedAdminMode);
      }
    };

    checkAdminMode();

    // Listen for admin mode changes
    const handleAdminModeChange = () => {
      checkAdminMode();
    };

    if (typeof window !== "undefined") {
      window.addEventListener("adminModeChanged", handleAdminModeChange);
      return () => {
        window.removeEventListener("adminModeChanged", handleAdminModeChange);
      };
    }
  }, []);

  const handleThemeToggle = (checked: boolean) => {
    setIsDarkMode(checked);
    setTheme(checked ? "dark" : "light");
  };

  return (
    <header
      className={
        isAdminPage
          ? "sticky top-0 z-50 border-b bg-background p-4 ml-64"
          : "sticky top-0 z-50 border-b bg-background p-4"
      }
    >
      <div className="flex items-center justify-between">
        {/* Logo and Title */}
        <Link
          href={isHydrated && adminMode ? "/admin" : "/"}
          className="flex items-center space-x-2"
        >
          <Image
            src="/images/LDIS.png"
            alt="LDIS"
            width={28}
            height={28}
            className="h-7 w-7"
          />
          <h1 className="text-xl font-extrabold">LDIS</h1>
        </Link>

        {/* Right side buttons */}
        <div className="flex items-center space-x-2">
          {/* Notification Dropdown */}
          <NotificationDropdown />

          {/* Burger Menu Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <Menu className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {/* Theme Toggle */}
              <div className="px-3 py-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {isDarkMode ? (
                      <Moon className="h-4 w-4" />
                    ) : (
                      <Sun className="h-4 w-4" />
                    )}
                    <Label htmlFor="theme-toggle" className="text-sm">
                      {isDarkMode ? "Dark Mode" : "Light Mode"}
                    </Label>
                  </div>
                  <Switch
                    id="theme-toggle"
                    checked={isDarkMode}
                    onCheckedChange={handleThemeToggle}
                  />
                </div>
              </div>

              <DropdownMenuSeparator />

              {/* Settings Button */}
              <DropdownMenuItem onClick={() => router.push("/settings")}>
                <Settings className="h-4 w-4 mr-2" />
                Settings
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
