"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Upload, FileText } from "lucide-react";
import { toast } from "sonner";
import { useNotifications } from "@/contexts/notification-context";

export default function NotificationUploadPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<string>("");

  const { addNotification } = useNotifications();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        toast.error("Please select a PDF file");
        return;
      }
      setSelectedFile(file);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error("Please select a PDF file first");
      return;
    }

    setIsUploading(true);
    setUploadProgress("Preparing upload...");

    try {
      // Parse the PDF to extract embedded data
      const formData = new FormData();
      formData.append("file", selectedFile);

      setUploadProgress("Uploading PDF file...");

      const parseResponse = await fetch("/api/pdf/parse", {
        method: "POST",
        body: formData,
      });

      if (!parseResponse.ok) {
        throw new Error(`Upload failed: ${parseResponse.status}`);
      }

      setUploadProgress("Processing PDF data...");
      const parseResult = await parseResponse.json();

      // Upload the PDF file to server
      setUploadProgress("Saving PDF file...");
      const uploadFormData = new FormData();
      uploadFormData.append("file", selectedFile);

      const uploadResponse = await fetch("/api/upload/pdf", {
        method: "POST",
        body: uploadFormData,
      });

      if (!uploadResponse.ok) {
        throw new Error("Failed to upload PDF file");
      }

      const uploadResult = await uploadResponse.json();

      // Create notification with PDF data and file path
      setUploadProgress("Creating notification...");
      addNotification({
        title: selectedFile.name,
        message: `File uploaded at ${new Date().toLocaleTimeString()}`,
        type: "success",
        pdfFileName: selectedFile.name,
        pdfFilePath: uploadResult.filePath,
        pdfData: parseResult.data,
      });

      toast.success("PDF uploaded successfully");

      // Reset form
      setSelectedFile(null);
      const fileInput = document.getElementById("pdf-file") as HTMLInputElement;
      if (fileInput) {
        fileInput.value = "";
      }
    } catch (error) {
      console.error("Error uploading PDF:", error);

      addNotification({
        title: "PDF Upload Failed",
        message: `Failed to upload "${selectedFile.name}". Please try again.`,
        type: "error",
        pdfFileName: selectedFile.name,
      });

      toast.error("Failed to upload PDF");
    } finally {
      setIsUploading(false);
      setUploadProgress("");
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-2xl">
      <div className="mb-6">
        <h1 className="text-3xl font-bold mb-2">
          Upload PDF for Notifications
        </h1>
        <p className="text-muted-foreground">
          Upload a PDF file to trigger a notification. This demonstrates the
          notification system functionality.
        </p>
      </div>

      {/* File Upload Section */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload PDF File
          </CardTitle>
          <CardDescription>
            Select a PDF file to upload. A notification will be created once the
            upload is complete.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="pdf-file">PDF File</Label>
            <Input
              id="pdf-file"
              type="file"
              accept=".pdf"
              onChange={handleFileSelect}
              className="mt-1"
              disabled={isUploading}
            />
          </div>

          {selectedFile && (
            <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
              <FileText className="h-4 w-4 text-blue-500" />
              <div className="flex-1">
                <p className="text-sm font-medium">{selectedFile.name}</p>
                <p className="text-xs text-muted-foreground">
                  {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                </p>
              </div>
            </div>
          )}

          <Button
            onClick={handleUpload}
            disabled={!selectedFile || isUploading}
            className="w-full"
          >
            {isUploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                {uploadProgress || "Uploading..."}
              </>
            ) : (
              <>
                <Upload className="h-4 w-4 mr-2" />
                Upload PDF
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How it works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>1. Select a PDF file using the file input above</p>
          <p>2. Click &quot;Upload PDF&quot; to start the upload process</p>
          <p>
            3. A notification will appear in the notification dropdown (bell
            icon in the header)
          </p>
          <p>4. Check the notification dropdown to see your upload status</p>
        </CardContent>
      </Card>
    </div>
  );
}
