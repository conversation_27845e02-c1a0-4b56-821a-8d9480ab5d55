"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { QrCode, Wifi, Download, Copy, Check } from "lucide-react";
import { toast } from "sonner";
import QRCode from "qrcode";

interface QRCodeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function QRCodeDialog({ open, onOpenChange }: QRCodeDialogProps) {
  const [ssid, setSsid] = useState("");
  const [password, setPassword] = useState("");
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [copied, setCopied] = useState(false);

  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      setSsid("");
      setPassword("");
      setQrCodeDataUrl("");
      setCopied(false);
    }
  }, [open]);

  const generateQRCode = async () => {
    if (!ssid.trim()) {
      toast.error("Please enter a WiFi network name (SSID)");
      return;
    }

    setIsGenerating(true);
    try {
      // Get the network IP from the server to ensure mobile devices can access it
      const response = await fetch("/api/network-ip");
      const { baseURL, success } = await response.json();

      if (!success) {
        toast.error("Could not detect network IP address");
        return;
      }

      // Create a URL that contains both WiFi connection info and auto-redirect
      // This approach works better than pure WiFi QR codes for auto-redirection
      const connectUrl = `${baseURL}/api/connect?ssid=${encodeURIComponent(
        ssid
      )}&password=${encodeURIComponent(password)}`;

      // Generate QR code for the connection URL
      const qrDataUrl = await QRCode.toDataURL(connectUrl, {
        width: 300,
        margin: 2,
        color: {
          dark: "#000000",
          light: "#FFFFFF",
        },
      });

      setQrCodeDataUrl(qrDataUrl);
      toast.success("Hotspot connection QR code generated successfully!");
    } catch (error) {
      console.error("Error generating QR code:", error);
      toast.error("Failed to generate QR code");
    } finally {
      setIsGenerating(false);
    }
  };

  const downloadQRCode = () => {
    if (!qrCodeDataUrl) return;

    const link = document.createElement("a");
    link.download = `hotspot-qr-${ssid}.png`;
    link.href = qrCodeDataUrl;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    toast.success("QR code downloaded!");
  };

  const copyToClipboard = async () => {
    if (!qrCodeDataUrl) return;

    try {
      // Convert data URL to blob
      const response = await fetch(qrCodeDataUrl);
      const blob = await response.blob();

      // Copy to clipboard
      await navigator.clipboard.write([
        new ClipboardItem({ "image/png": blob }),
      ]);

      setCopied(true);
      toast.success("QR code copied to clipboard!");

      // Reset copied state after 2 seconds
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      toast.error("Failed to copy QR code");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            Generate Hotspot QR Code
          </DialogTitle>
          <DialogDescription>
            Create a QR code that provides WiFi connection instructions and
            automatically redirects mobile devices to the upload page.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Hotspot Configuration */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Wifi className="h-4 w-4" />
                Hotspot Network Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <Label htmlFor="ssid">Network Name (SSID)</Label>
                <Input
                  id="ssid"
                  value={ssid}
                  onChange={(e) => setSsid(e.target.value)}
                  placeholder="Enter your WiFi network name"
                  className="mt-1"
                />
              </div>
              <div>
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter your WiFi password"
                  className="mt-1"
                />
              </div>
              <Button
                onClick={generateQRCode}
                disabled={isGenerating}
                className="w-full"
              >
                {isGenerating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    <QrCode className="h-4 w-4 mr-2" />
                    Generate QR Code
                  </>
                )}
              </Button>
            </CardContent>
          </Card>

          {/* QR Code Display */}
          {qrCodeDataUrl && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Generated QR Code</CardTitle>
                <CardDescription className="text-xs">
                  Scan this code with a mobile device to get WiFi connection
                  instructions and be automatically redirected to the upload
                  page.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-center">
                  <img
                    src={qrCodeDataUrl}
                    alt="Hotspot QR Code"
                    className="border rounded-lg"
                  />
                </div>
                <div className="flex gap-2">
                  <Button
                    onClick={copyToClipboard}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    {copied ? (
                      <>
                        <Check className="h-4 w-4 mr-2" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="h-4 w-4 mr-2" />
                        Copy
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={downloadQRCode}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
            <CardContent className="pt-4">
              <div className="space-y-2 text-sm">
                <p className="font-medium text-blue-800 dark:text-blue-200">
                  How to use:
                </p>
                <ol className="list-decimal list-inside space-y-1 text-blue-700 dark:text-blue-300">
                  <li>
                    Share your device's internet connection as a WiFi hotspot
                  </li>
                  <li>
                    Use the network name and password from your hotspot settings
                    above
                  </li>
                  <li>Generate and share the QR code with mobile users</li>
                  <li>Users scan the QR code with their mobile device</li>
                  <li>
                    They'll see a connection page with WiFi details and
                    instructions
                  </li>
                  <li>
                    After connecting to your hotspot, they'll be automatically
                    redirected to the upload page
                  </li>
                </ol>
                <div className="mt-3 p-2 bg-green-100 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <p className="text-xs text-green-800 dark:text-green-200">
                    <strong>✓ Improved:</strong> This QR code now automatically
                    opens a connection page that guides users through the WiFi
                    setup and redirects them to the upload page!
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
