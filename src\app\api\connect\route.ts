import { NextRequest, NextResponse } from 'next/server';
import { getBaseURL } from '@/lib/network-utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const ssid = searchParams.get('ssid') || '';
    const password = searchParams.get('password') || '';

    // Get the dynamic base URL for this request
    const baseURL = getBaseURL(request);

    // Get the user agent to detect device type
    const userAgent = request.headers.get('user-agent') || '';
    const isIOS = /iPhone|iPad|iPod/i.test(userAgent);
    const isAndroid = /Android/i.test(userAgent);
    const isMobile = isIOS || isAndroid;

    // Create WiFi connection string for different platforms
    const wifiString = `WIFI:T:WPA;S:${ssid};P:${password};H:false;;`;
    
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LDIS - Connect to Hotspot</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .step {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: left;
        }
        .step-number {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 10px;
        }
        .wifi-info {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            word-break: break-all;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            margin: 10px 5px;
            cursor: pointer;
        }
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        .primary-button {
            background: rgba(76, 175, 80, 0.8);
            border-color: rgba(76, 175, 80, 1);
        }
        .primary-button:hover {
            background: rgba(76, 175, 80, 1);
        }
        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .hidden { display: none; }
        .success { color: #4CAF50; }
        .auto-redirect {
            margin-top: 20px;
            padding: 15px;
            background: rgba(76, 175, 80, 0.2);
            border-radius: 10px;
            border: 1px solid rgba(76, 175, 80, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">LDIS</div>
        <h2>Connect to Hotspot</h2>
        
        <div class="step">
            <div><span class="step-number">1</span><strong>Connect to WiFi Network:</strong></div>
            <div class="wifi-info">
                <div><strong>Network:</strong> ${ssid}</div>
                ${password ? `<div><strong>Password:</strong> ${password}</div>` : '<div><em>No password required</em></div>'}
            </div>
        </div>
        
        <div class="step">
            <div><span class="step-number">2</span><strong>Access Upload Page:</strong></div>
            <div>Once connected, you'll be automatically redirected to the upload page.</div>
        </div>
        
        ${isMobile ? `
        <div style="margin: 20px 0;">
            <button class="button primary-button" onclick="connectToWifi()">
                <span id="connect-spinner" class="spinner hidden"></span>
                <span id="connect-text">Connect to WiFi</span>
            </button>
        </div>
        ` : ''}
        
        <div class="auto-redirect" id="redirect-section" style="display: none;">
            <div class="spinner"></div>
            <div>Connected! Redirecting to upload page...</div>
        </div>
        
        <div style="margin-top: 20px;">
            <a href="${baseURL}/notifications/upload" class="button">
                Go to Upload Page
            </a>
        </div>
    </div>
    
    <script>
        let checkInterval;
        let redirectTimeout;
        
        function connectToWifi() {
            const spinner = document.getElementById('connect-spinner');
            const text = document.getElementById('connect-text');
            
            spinner.classList.remove('hidden');
            text.textContent = 'Connecting...';
            
            // For mobile devices, try to trigger WiFi connection
            ${isMobile ? `
            // Create a data URL for WiFi connection
            const wifiData = "${wifiString}";
            
            // Try to open WiFi settings or connection
            if (navigator.share) {
                navigator.share({
                    title: 'WiFi Connection',
                    text: 'Connect to: ${ssid}',
                    url: 'data:text/plain;charset=utf-8,' + encodeURIComponent(wifiData)
                }).catch(console.error);
            }
            ` : ''}
            
            // Start checking for connection
            startConnectionCheck();
        }
        
        function startConnectionCheck() {
            // Check if we can reach the upload page (indicating successful connection)
            checkInterval = setInterval(async () => {
                try {
                    const response = await fetch('${baseURL}/notifications/upload', {
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    
                    // If we can reach it, show redirect section
                    clearInterval(checkInterval);
                    showRedirectSection();
                } catch (error) {
                    // Still trying to connect
                    console.log('Still connecting...');
                }
            }, 2000);
            
            // Auto-redirect after 10 seconds regardless
            redirectTimeout = setTimeout(() => {
                clearInterval(checkInterval);
                showRedirectSection();
            }, 10000);
        }
        
        function showRedirectSection() {
            document.getElementById('redirect-section').style.display = 'block';
            
            // Redirect after 3 seconds
            setTimeout(() => {
                window.location.href = '${baseURL}/notifications/upload';
            }, 3000);
        }
        
        // Auto-start connection check if user seems to be on the same network
        window.addEventListener('load', () => {
            // Try to detect if already connected by checking if we can reach the server
            fetch('${baseURL}/api/captive-portal', {
                method: 'HEAD',
                mode: 'no-cors'
            }).then(() => {
                // Already connected, show redirect
                setTimeout(showRedirectSection, 1000);
            }).catch(() => {
                // Not connected yet, wait for user action
            });
        });
    </script>
</body>
</html>`;

    return new NextResponse(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error in connect endpoint:', error);
    
    // Fallback redirect
    return NextResponse.redirect('http://localhost:3000/notifications/upload');
  }
}

export async function POST(request: NextRequest) {
  return GET(request);
}
