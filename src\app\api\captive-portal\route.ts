import { NextRequest, NextResponse } from 'next/server';
import { getBaseURL } from '@/lib/network-utils';

// Simple captive portal endpoint that redirects to the upload page
export async function GET(request: NextRequest) {
  try {
    // Get the dynamic base URL for this request
    const baseURL = getBaseURL(request);

    // Get the user agent to detect if it's a mobile device
    const userAgent = request.headers.get('user-agent') || '';
    const isMobile = /Mobile|Android|iPhone|iPad/i.test(userAgent);
    
    // Create a simple HTML page that automatically redirects to the upload page
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LDIS - Redirecting...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 1rem;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 1rem auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .message {
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }
        .redirect-link {
            display: inline-block;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        .redirect-link:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
    </style>
    <script>
        // Automatically redirect after 3 seconds
        setTimeout(function() {
            window.location.href = '${baseURL}/notifications/upload';
        }, 3000);

        // Also try to redirect immediately for better UX
        window.addEventListener('load', function() {
            setTimeout(function() {
                window.location.href = '${baseURL}/notifications/upload';
            }, 1000);
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="logo">LDIS</div>
        <div class="spinner"></div>
        <div class="message">
            Welcome! You're connected to the hotspot.
            <br>
            Redirecting to upload page...
        </div>
        <a href="${baseURL}/notifications/upload" class="redirect-link">
            Click here if not redirected automatically
        </a>
    </div>
</body>
</html>`;

    return new NextResponse(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error in captive portal:', error);
    
    // Fallback redirect
    return NextResponse.redirect(`${getBaseURL(request)}/notifications/upload`);
  }
}

// Handle POST requests (some captive portal detection might use POST)
export async function POST(request: NextRequest) {
  return GET(request);
}
